<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit"></i> Edit Country
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/countries') ?>">Countries</a></li>
                    <li class="breadcrumb-item active">Edit <?= esc($country['name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('dakoii/locations/countries') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Countries
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->get('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> Please fix the following errors:
            <ul class="mb-0 mt-2">
                <?php foreach (session()->get('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Edit Country Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-globe"></i> Country Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('dakoii/locations/countries/' . $country['id'] . '/update') ?>" method="POST">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        Country Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control <?= session()->get('errors.name') ? 'is-invalid' : '' ?>" 
                                           id="name" 
                                           name="name" 
                                           value="<?= old('name', $country['name']) ?>" 
                                           placeholder="Enter country name"
                                           required>
                                    <?php if (session()->get('errors.name')): ?>
                                        <div class="invalid-feedback">
                                            <?= session()->get('errors.name') ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">
                                        Country Code <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control <?= session()->get('errors.code') ? 'is-invalid' : '' ?>" 
                                           id="code" 
                                           name="code" 
                                           value="<?= old('code', $country['code']) ?>" 
                                           placeholder="e.g., PNG, AU, NZ"
                                           maxlength="10"
                                           style="text-transform: uppercase;"
                                           required>
                                    <?php if (session()->get('errors.code')): ?>
                                        <div class="invalid-feedback">
                                            <?= session()->get('errors.code') ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="form-text">
                                        Enter a unique country code (2-10 characters)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" 
                                              id="description" 
                                              name="description" 
                                              rows="3" 
                                              placeholder="Optional description about the country"><?= old('description', $country['description'] ?? '') ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Country Info -->
                        <div class="row">
                            <div class="col-12">
                                <div class="bg-light p-3 rounded mb-3">
                                    <h6 class="mb-2">Country Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">Created:</small><br>
                                            <span><?= date('M d, Y H:i', strtotime($country['created_at'])) ?></span>
                                        </div>
                                        <?php if (!empty($country['updated_at'])): ?>
                                        <div class="col-md-6">
                                            <small class="text-muted">Last Updated:</small><br>
                                            <span><?= date('M d, Y H:i', strtotime($country['updated_at'])) ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/locations/countries') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Country
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase country code
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const code = document.getElementById('code').value.trim();

            if (!name || !code) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }

            if (code.length < 2) {
                e.preventDefault();
                alert('Country code must be at least 2 characters long.');
                return false;
            }
        });
    }
});
</script>

<?= $this->endSection() ?>
